<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<jsp:include page="/WEB-INF/views/common/layout.jsp">
    <jsp:param name="title" value="视频流" />
    <jsp:param name="content" value="/WEB-INF/views/camera/stream-content.jsp" />
    <jsp:param name="additionalStyles" value="
        /* 视频流页面样式 */
        .stream-container {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 20px;
            overflow: hidden;
            position: relative;
            height: calc(100vh - 200px);
            min-height: 500px;
            box-shadow:
                0 10px 30px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .stream-container img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 16px;
        }
        .stream-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.8) 0%,
                rgba(26, 26, 26, 0.9) 50%,
                rgba(0, 0, 0, 0.8) 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            backdrop-filter: blur(10px);
        }
        .stream-controls {
            position: absolute;
            bottom: 30px;
            left: 0;
            width: 100%;
            padding: 0 30px;
            display: flex;
            justify-content: center;
            z-index: 10;
            gap: 15px;
        }
        .stream-controls .btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4rem;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        .stream-controls .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .stream-controls .btn:hover::before {
            opacity: 1;
        }
        .stream-controls .btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        .stream-controls .btn-primary {
            background: linear-gradient(135deg, rgba(13, 110, 253, 0.8), rgba(10, 88, 202, 0.9));
            border-color: rgba(13, 110, 253, 0.6);
        }
        .stream-controls .btn-primary:hover {
            background: linear-gradient(135deg, rgba(13, 110, 253, 0.9), rgba(10, 88, 202, 1));
            border-color: rgba(13, 110, 253, 0.8);
        }
        .stream-info {
            position: absolute;
            top: 25px;
            left: 25px;
            color: white;
            z-index: 10;
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.7) 0%,
                rgba(26, 26, 26, 0.8) 100%);
            padding: 15px 20px;
            border-radius: 15px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }
        .stream-info:hover {
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.8) 0%,
                rgba(26, 26, 26, 0.9) 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }
        .camera-status {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            box-shadow: 0 0 8px rgba(255,255,255,0.3);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 8px rgba(255,255,255,0.3); }
            50% { box-shadow: 0 0 15px rgba(255,255,255,0.6); }
            100% { box-shadow: 0 0 8px rgba(255,255,255,0.3); }
        }
        .camera-online {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        .camera-offline {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }
        /* 视频播放器美化 */
        #videoPlayer {
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }
        #videoPlayer:hover {
            box-shadow: 0 6px 25px rgba(0,0,0,0.4);
        }
        /* 状态消息美化 */
        #statusMessage {
            font-size: 1.1rem;
            font-weight: 500;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            margin-top: 15px;
        }
        /* 流状态覆盖层图标动画 */
        .stream-overlay i {
            animation: breathe 2s ease-in-out infinite;
        }
        @keyframes breathe {
            0%, 100% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1);
                opacity: 1;
            }
        }
        /* 加载指示器 */
        .loading-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    " />
    <jsp:param name="scripts" value="
        <script src='https://cdn.jsdelivr.net/npm/hls.js@latest'></script>
        <script>
            let hlsPlayer = null;
            let currentStreamUrl = null;

            // 页面加载完成后执行
            document.addEventListener('DOMContentLoaded', function() {
                // 获取视频流URL
                const cameraId = '${camera.id}';

                // 如果摄像头在线，检查流状态
                if (${camera.status} === 1) {
                    checkStreamStatus(cameraId);
                }
                
                // 流控制按钮事件
                const startStreamBtn = document.getElementById('startStreamBtn');
                if (startStreamBtn) {
                    startStreamBtn.addEventListener('click', () => startStream(cameraId));
                }

                const stopStreamBtn = document.getElementById('stopStreamBtn');
                if (stopStreamBtn) {
                    stopStreamBtn.addEventListener('click', () => stopStream(cameraId));
                }

                const restartStreamBtn = document.getElementById('restartStreamBtn');
                if (restartStreamBtn) {
                    restartStreamBtn.addEventListener('click', () => restartStream(cameraId));
                }

                const refreshStreamBtn = document.getElementById('refreshStreamBtn');
                if (refreshStreamBtn) {
                    refreshStreamBtn.addEventListener('click', () => checkStreamStatus(cameraId));
                }

                // 控制按钮点击事件
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');
                        controlCamera(cameraId, action);
                    });
                });

                // 连接/断开摄像头按钮点击事件
                const connectBtn = document.getElementById('connectBtn');
                if (connectBtn) {
                    connectBtn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');

                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }

                // 断开摄像头按钮事件
                const disconnectBtn = document.getElementById('disconnectBtn');
                if (disconnectBtn) {
                    disconnectBtn.addEventListener('click', function() {
                        const action = this.getAttribute('data-action');

                        fetch('${pageContext.request.contextPath}/camera/control', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'cameraId=' + cameraId + '&action=' + action
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert(data.message);
                                // 刷新页面
                                window.location.reload();
                            } else {
                                alert('操作失败: ' + data.message);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('操作失败，请稍后重试');
                        });
                    });
                }
                
                // 全屏按钮点击事件
                const fullscreenBtn = document.getElementById('fullscreenBtn');
                if (fullscreenBtn) {
                    fullscreenBtn.addEventListener('click', function() {
                        const streamContainer = document.querySelector('.stream-container');
                        if (streamContainer) {
                            if (streamContainer.requestFullscreen) {
                                streamContainer.requestFullscreen();
                            } else if (streamContainer.webkitRequestFullscreen) { /* Safari */
                                streamContainer.webkitRequestFullscreen();
                            } else if (streamContainer.msRequestFullscreen) { /* IE11 */
                                streamContainer.msRequestFullscreen();
                            }
                        }
                    });
                }
            });
            
            // 检查流状态
            function checkStreamStatus(cameraId) {
                updateStatusMessage('正在检查流状态...');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=getStreamUrl'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.streamUrl) {
                        updateStatusMessage('检测到活动流，正在加载播放器...');
                        loadHLSPlayer(data.streamUrl);
                        showStreamControls();
                    } else {
                        updateStatusMessage('未检测到活动流，请启动视频流');
                        hideStreamControls();
                    }
                })
                .catch(error => {
                    console.error('检查流状态失败:', error);
                    updateStatusMessage('检查流状态失败，请稍后重试');
                });
            }

            // 启动视频流
            function startStream(cameraId) {
                updateStatusMessage('正在启动视频流...<span class="loading-indicator"></span>');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=startStream'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatusMessage('视频流启动成功，FFmpeg正在后台处理...<span class="loading-indicator"></span>');
                        showStreamControls();

                        // 立即尝试加载播放器
                        if (data.streamUrl) {
                            setTimeout(() => {
                                loadHLSPlayer(data.streamUrl);
                            }, 2000);
                        }

                        // 定期检查流状态，直到流可用
                        let checkCount = 0;
                        const maxChecks = 20; // 最多检查20次（约2分钟）
                        const checkInterval = setInterval(() => {
                            checkCount++;
                            console.log('检查流状态，第' + checkCount + '次');

                            fetch('${pageContext.request.contextPath}/camera/stream', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: 'cameraId=' + cameraId + '&action=getStreamUrl'
                            })
                            .then(response => response.json())
                            .then(statusData => {
                                if (statusData.success && statusData.streamUrl) {
                                    clearInterval(checkInterval);
                                    updateStatusMessage('检测到活动流，正在加载播放器...');
                                    loadHLSPlayer(statusData.streamUrl);
                                } else if (checkCount >= maxChecks) {
                                    clearInterval(checkInterval);
                                    updateStatusMessage('流启动超时，请检查摄像头连接或重试');
                                } else {
                                    updateStatusMessage('等待流启动中...' + checkCount + '/' + maxChecks + '<span class="loading-indicator"></span>');
                                }
                            })
                            .catch(error => {
                                console.error('检查流状态失败:', error);
                                if (checkCount >= maxChecks) {
                                    clearInterval(checkInterval);
                                    updateStatusMessage('流状态检查失败，请重试');
                                }
                            });
                        }, 6000); // 每6秒检查一次

                    } else {
                        updateStatusMessage('启动视频流失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('启动视频流失败:', error);
                    updateStatusMessage('启动视频流失败，请稍后重试');
                });
            }

            // 停止视频流
            function stopStream(cameraId) {
                updateStatusMessage('正在停止视频流...');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=stopStream'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatusMessage('视频流已停止');
                        stopHLSPlayer();
                        hideStreamControls();
                    } else {
                        updateStatusMessage('停止视频流失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('停止视频流失败:', error);
                    updateStatusMessage('停止视频流失败，请稍后重试');
                });
            }

            // 重启视频流
            function restartStream(cameraId) {
                updateStatusMessage('正在重启视频流...');

                fetch('${pageContext.request.contextPath}/camera/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=restartStream'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatusMessage('视频流重启成功，正在加载播放器...');
                        if (data.streamUrl) {
                            setTimeout(() => {
                                loadHLSPlayer(data.streamUrl);
                                showStreamControls();
                            }, 5000); // 等待5秒让流稳定
                        } else {
                            setTimeout(() => checkStreamStatus(cameraId), 5000);
                        }
                    } else {
                        updateStatusMessage('重启视频流失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('重启视频流失败:', error);
                    updateStatusMessage('重启视频流失败，请稍后重试');
                });
            }
            
            // 加载HLS播放器
            function loadHLSPlayer(streamUrl) {
                const video = document.getElementById('videoPlayer');
                const streamStatus = document.getElementById('streamStatus');

                if (!streamUrl) {
                    console.error('流URL为空');
                    return;
                }

                console.log('加载HLS流:', streamUrl);

                // 清理现有播放器
                if (hlsPlayer) {
                    hlsPlayer.destroy();
                    hlsPlayer = null;
                }

                if (Hls.isSupported()) {
                    hlsPlayer = new Hls({
                        debug: false,
                        enableWorker: true,
                        lowLatencyMode: true,
                        backBufferLength: 30,
                        maxBufferLength: 20,
                        maxMaxBufferLength: 30,
                        liveSyncDurationCount: 3,
                        liveMaxLatencyDurationCount: 6,
                        manifestLoadingTimeOut: 10000,
                        manifestLoadingMaxRetry: 4,
                        manifestLoadingRetryDelay: 500,
                        levelLoadingTimeOut: 10000,
                        levelLoadingMaxRetry: 4,
                        levelLoadingRetryDelay: 500,
                        fragLoadingTimeOut: 20000,
                        fragLoadingMaxRetry: 6,
                        fragLoadingRetryDelay: 500,
                        startLevel: -1,
                        capLevelToPlayerSize: false
                    });

                    hlsPlayer.loadSource(streamUrl);
                    hlsPlayer.attachMedia(video);

                    hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
                        console.log('HLS清单解析完成，开始播放');
                        updateStatusMessage('视频流加载成功！');

                        // 隐藏状态覆盖层，显示视频
                        streamStatus.style.display = 'none';
                        video.style.display = 'block';

                        video.play().catch(e => {
                            console.log('自动播放失败:', e.message);
                            updateStatusMessage('视频已准备就绪，请点击播放按钮');
                        });
                    });

                    hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
                        console.log('HLS错误:', data.type, '-', data.details);

                        if (data.fatal) {
                            switch(data.type) {
                                case Hls.ErrorTypes.NETWORK_ERROR:
                                    console.log('网络错误，尝试重新加载...');
                                    updateStatusMessage('网络错误，正在重试...');
                                    setTimeout(() => {
                                        if (hlsPlayer) {
                                            hlsPlayer.startLoad();
                                        }
                                    }, 1000);
                                    break;
                                case Hls.ErrorTypes.MEDIA_ERROR:
                                    console.log('媒体错误，尝试恢复...');
                                    updateStatusMessage('媒体错误，正在恢复...');
                                    if (hlsPlayer) {
                                        hlsPlayer.recoverMediaError();
                                    }
                                    break;
                                default:
                                    console.log('致命错误，等待流重启...');
                                    updateStatusMessage('播放错误，请尝试重启视频流');
                                    break;
                            }
                        }
                    });

                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                    console.log('使用Safari原生HLS支持');
                    video.src = streamUrl;
                    video.addEventListener('loadedmetadata', () => {
                        console.log('元数据加载完成，开始播放');
                        updateStatusMessage('视频流加载成功！');

                        streamStatus.style.display = 'none';
                        video.style.display = 'block';
                        video.play();
                    });
                } else {
                    updateStatusMessage('浏览器不支持HLS播放');
                }

                currentStreamUrl = streamUrl;
            }

            // 停止HLS播放器
            function stopHLSPlayer() {
                const video = document.getElementById('videoPlayer');
                const streamStatus = document.getElementById('streamStatus');

                if (hlsPlayer) {
                    hlsPlayer.destroy();
                    hlsPlayer = null;
                }

                video.src = '';
                video.style.display = 'none';
                streamStatus.style.display = 'flex';

                currentStreamUrl = null;
            }

            // 更新状态消息
            function updateStatusMessage(message) {
                const statusMessage = document.getElementById('statusMessage');
                if (statusMessage) {
                    statusMessage.innerHTML = message;
                }
                console.log('状态:', message.replace(/<[^>]*>/g, '')); // 日志中移除HTML标签
            }

            // 显示流控制按钮
            function showStreamControls() {
                const stopBtn = document.getElementById('stopStreamBtn');
                const restartBtn = document.getElementById('restartStreamBtn');

                if (stopBtn) stopBtn.style.display = 'block';
                if (restartBtn) restartBtn.style.display = 'block';
            }

            // 隐藏流控制按钮
            function hideStreamControls() {
                const stopBtn = document.getElementById('stopStreamBtn');
                const restartBtn = document.getElementById('restartStreamBtn');

                if (stopBtn) stopBtn.style.display = 'none';
                if (restartBtn) restartBtn.style.display = 'none';
            }

            // 控制摄像头
            function controlCamera(cameraId, action) {
                fetch('${pageContext.request.contextPath}/camera/control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'cameraId=' + cameraId + '&action=' + action
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('控制成功:', data.message);
                    } else {
                        console.error('控制失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        </script>
    " />
</jsp:include>
