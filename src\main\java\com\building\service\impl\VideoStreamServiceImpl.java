package com.building.service.impl;

import com.building.dao.CameraDao;
import com.building.model.Camera;
import com.building.service.VideoStreamService;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 视频流服务实现类
 */
public class VideoStreamServiceImpl implements VideoStreamService {
    
    private CameraDao cameraDao;
    private Gson gson;

    // 流服务器配置
    private static final String STREAM_SERVER_URL = "http://127.0.0.1:3001";
    private static final int CONNECTION_TIMEOUT = 15000;
    private static final int READ_TIMEOUT = 30000; // 增加到30秒，给FFmpeg足够的启动时间

    // 流服务器进程管理
    private static Process streamServerProcess = null;
    private static final String NODE_COMMAND = "node";
    private static final String STREAM_SERVER_SCRIPT = "stream-server.js";

    // 用户手动停止标志
    private static boolean userManuallyStopped = false;
    
    /**
     * 构造函数
     */
    public VideoStreamServiceImpl() {
        cameraDao = new CameraDao();
        gson = new Gson();
    }
    
    @Override
    public boolean startCameraStream(int cameraId) {
        System.out.println("开始启动摄像头流: " + cameraId);

        try {
            // 首先检查流服务器是否可用
            if (!checkStreamServerHealth()) {
                System.err.println("流服务器不可用，请确保Node.js流服务器已启动");
                return false;
            }

            Camera camera = cameraDao.getCameraById(cameraId);
            if (camera == null) {
                System.err.println("摄像头不存在: " + cameraId);
                return false;
            }

            if (camera.getRtspUrl() == null || camera.getRtspUrl().trim().isEmpty()) {
                System.err.println("摄像头RTSP URL为空: " + cameraId);
                return false;
            }

            // 生成流ID（如果不存在）
            String streamId = camera.getStreamId();
            if (streamId == null || streamId.trim().isEmpty()) {
                streamId = "camera_" + cameraId;
                camera.setStreamId(streamId);
            }

            System.out.println("准备启动流 - 摄像头ID: " + cameraId + ", 流ID: " + streamId + ", RTSP URL: " + camera.getRtspUrl());

            // 准备请求数据
            JsonObject requestData = new JsonObject();
            requestData.addProperty("rtspUrl", camera.getRtspUrl());
            requestData.addProperty("streamId", streamId);
            requestData.addProperty("format", camera.getStreamFormat() != null ? camera.getStreamFormat() : "hls");

            System.out.println("发送请求到流服务器: " + STREAM_SERVER_URL + "/api/stream/start");
            System.out.println("请求数据: " + requestData.toString());

            // 发送启动流请求
            String response = sendHttpRequest("/api/stream/start", "POST", requestData.toString());
            System.out.println("收到响应: " + response);

            JsonObject responseObj = gson.fromJson(response, JsonObject.class);
            
            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                // 更新数据库中的流状态
                JsonObject data = responseObj.getAsJsonObject("data");
                String streamUrl = data.get("outputUrl").getAsString();

                // 先设置为启动中状态
                cameraDao.updateCameraStreamStatus(cameraId, 1, streamUrl);

                System.out.println("摄像头流启动请求成功: " + cameraId + ", 流URL: " + streamUrl);
                System.out.println("FFmpeg进程正在后台启动，请稍等片刻后检查流状态");

                // 启动后台任务检查流状态
                checkStreamStatusAsync(cameraId, streamUrl);

                return true;
            } else {
                String error = responseObj != null && responseObj.has("error") ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("启动摄像头流失败: " + cameraId + ", 错误: " + error);

                // 更新错误状态
                cameraDao.updateCameraStreamStatus(cameraId, 2, null);
                cameraDao.incrementStreamErrorCount(cameraId);

                return false;
            }
        } catch (Exception e) {
            System.err.println("启动摄像头流异常: " + cameraId + ", " + e.getMessage());
            e.printStackTrace();
            
            // 更新错误状态
            cameraDao.updateCameraStreamStatus(cameraId, 2, null);
            cameraDao.incrementStreamErrorCount(cameraId);
            
            return false;
        }
    }
    
    @Override
    public boolean stopCameraStream(int cameraId) {
        try {
            Camera camera = cameraDao.getCameraById(cameraId);
            if (camera == null) {
                System.err.println("摄像头不存在: " + cameraId);
                return false;
            }
            
            String streamId = camera.getStreamId();
            if (streamId == null || streamId.trim().isEmpty()) {
                System.out.println("摄像头流ID为空，无需停止: " + cameraId);
                return true;
            }
            
            // 准备请求数据
            JsonObject requestData = new JsonObject();
            requestData.addProperty("streamId", streamId);
            
            // 发送停止流请求
            String response = sendHttpRequest("/api/stream/stop", "POST", requestData.toString());
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);
            
            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                // 更新数据库中的流状态
                cameraDao.updateCameraStreamStatus(cameraId, 0, null);
                
                System.out.println("摄像头流停止成功: " + cameraId);
                return true;
            } else {
                String error = responseObj != null ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("停止摄像头流失败: " + cameraId + ", 错误: " + error);
                return false;
            }
        } catch (Exception e) {
            System.err.println("停止摄像头流异常: " + cameraId + ", " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean restartCameraStream(int cameraId) {
        System.out.println("重启摄像头流: " + cameraId);
        
        // 先停止，再启动
        stopCameraStream(cameraId);
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return startCameraStream(cameraId);
    }
    
    @Override
    public String getCameraStreamStatus(int cameraId) {
        try {
            Camera camera = cameraDao.getCameraById(cameraId);
            if (camera == null) {
                return "摄像头不存在";
            }
            
            switch (camera.getStreamStatus()) {
                case 0:
                    return "未启动";
                case 1:
                    return "运行中";
                case 2:
                    return "错误";
                default:
                    return "未知状态";
            }
        } catch (Exception e) {
            System.err.println("获取摄像头流状态异常: " + cameraId + ", " + e.getMessage());
            return "获取状态失败";
        }
    }

    /**
     * 异步检查流状态
     * @param cameraId 摄像头ID
     * @param streamUrl 流URL
     */
    private void checkStreamStatusAsync(int cameraId, String streamUrl) {
        // 使用新线程异步检查
        new Thread(() -> {
            try {
                // 等待10秒让FFmpeg启动
                Thread.sleep(10000);

                // 检查流是否真正可用
                boolean streamAvailable = checkStreamAvailability(streamUrl);

                if (streamAvailable) {
                    System.out.println("摄像头流状态检查：流可用 - " + cameraId);
                    // 流状态已经在启动时设置为1，这里不需要再更新
                } else {
                    System.err.println("摄像头流状态检查：流不可用 - " + cameraId);
                    // 更新为错误状态
                    cameraDao.updateCameraStreamStatus(cameraId, 2, null);
                    cameraDao.incrementStreamErrorCount(cameraId);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("流状态检查被中断: " + cameraId);
            } catch (Exception e) {
                System.err.println("流状态检查异常: " + cameraId + ", " + e.getMessage());
                // 更新为错误状态
                cameraDao.updateCameraStreamStatus(cameraId, 2, null);
                cameraDao.incrementStreamErrorCount(cameraId);
            }
        }).start();
    }

    /**
     * 检查流是否可用
     * @param streamUrl 流URL
     * @return 是否可用
     */
    private boolean checkStreamAvailability(String streamUrl) {
        try {
            // 这里可以实现更复杂的流可用性检查
            // 简单起见，我们检查流文件是否存在
            if (streamUrl != null && streamUrl.contains("playlist.m3u8")) {
                // 对于HLS流，检查playlist文件是否存在
                String filePath = streamUrl.replace("http://localhost:3001/", "public/");
                java.io.File file = new java.io.File(filePath);
                return file.exists();
            }
            return true; // 其他情况默认认为可用
        } catch (Exception e) {
            System.err.println("检查流可用性异常: " + e.getMessage());
            return false;
        }
    }

    @Override
    public boolean checkStreamServerHealth() {
        System.out.println("检查流服务器健康状态: " + STREAM_SERVER_URL + "/api/health");

        try {
            String response = sendHttpRequest("/api/health", "GET", null);
            System.out.println("健康检查响应: " + response);

            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            boolean isHealthy = responseObj != null && responseObj.get("success").getAsBoolean();
            System.out.println("流服务器健康状态: " + (isHealthy ? "健康" : "不健康"));

            return isHealthy;
        } catch (Exception e) {
            System.err.println("检查流服务器健康状态失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public String getCameraStreamUrl(Camera camera) {
        if (camera == null || camera.getStreamUrl() == null) {
            return null;
        }
        
        // 返回完整的流URL
        return STREAM_SERVER_URL + camera.getStreamUrl();
    }
    
    /**
     * 发送HTTP请求
     * @param endpoint 端点
     * @param method 请求方法
     * @param requestBody 请求体
     * @return 响应内容
     * @throws IOException 如果发生IO异常
     */
    private String sendHttpRequest(String endpoint, String method, String requestBody) throws IOException {
        String fullUrl = STREAM_SERVER_URL + endpoint;
        System.out.println("发送HTTP请求: " + method + " " + fullUrl);

        URL url = new URL(fullUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod(method);
        connection.setConnectTimeout(CONNECTION_TIMEOUT);
        connection.setReadTimeout(READ_TIMEOUT);
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");

        System.out.println("连接超时: " + CONNECTION_TIMEOUT + "ms, 读取超时: " + READ_TIMEOUT + "ms");
        
        if (requestBody != null && ("POST".equals(method) || "PUT".equals(method))) {
            System.out.println("发送请求体: " + requestBody);
            connection.setDoOutput(true);
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
        }

        // 尝试连接并获取响应码
        int responseCode;
        try {
            responseCode = connection.getResponseCode();
            System.out.println("HTTP响应码: " + responseCode);
        } catch (IOException e) {
            System.err.println("获取响应码失败: " + e.getMessage());
            throw e;
        }

        // 读取响应
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(
                responseCode >= 200 && responseCode < 300
                    ? connection.getInputStream()
                    : connection.getErrorStream(),
                StandardCharsets.UTF_8))) {

            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        String responseStr = response.toString();
        System.out.println("HTTP响应内容: " + responseStr);

        return responseStr;
    }

    /**
     * 获取项目根目录
     * @return 项目根目录路径
     */
    private String getProjectRootDirectory() {
        // 尝试多种方法获取项目根目录

        // 方法1: 通过类路径获取
        try {
            String classPath = this.getClass().getProtectionDomain().getCodeSource().getLocation().getPath();
            System.out.println("类路径: " + classPath);

            // 从类路径向上查找项目根目录
            File currentDir = new File(classPath).getParentFile();
            while (currentDir != null) {
                File streamServerFile = new File(currentDir, STREAM_SERVER_SCRIPT);
                if (streamServerFile.exists()) {
                    System.out.println("通过类路径找到项目根目录: " + currentDir.getAbsolutePath());
                    return currentDir.getAbsolutePath();
                }
                currentDir = currentDir.getParentFile();
            }
        } catch (Exception e) {
            System.out.println("通过类路径获取项目根目录失败: " + e.getMessage());
        }

        // 方法2: 使用系统属性
        String[] systemProperties = {
            "user.dir",
            "catalina.base",
            "catalina.home"
        };

        for (String prop : systemProperties) {
            String path = System.getProperty(prop);
            if (path != null) {
                System.out.println("尝试系统属性 " + prop + ": " + path);
                File streamServerFile = new File(path, STREAM_SERVER_SCRIPT);
                if (streamServerFile.exists()) {
                    System.out.println("通过系统属性找到项目根目录: " + path);
                    return path;
                }
            }
        }

        // 方法3: 硬编码的已知路径
        String knownPath = "C:\\Users\\<USER>\\Desktop\\EduFusionCenter";
        File knownFile = new File(knownPath, STREAM_SERVER_SCRIPT);
        if (knownFile.exists()) {
            System.out.println("使用已知路径: " + knownPath);
            return knownPath;
        }

        // 默认返回当前工作目录
        String defaultPath = System.getProperty("user.dir");
        System.out.println("使用默认路径: " + defaultPath);
        return defaultPath;
    }

    @Override
    public boolean startStreamServer() {
        System.out.println("尝试启动流服务器...");

        // 检查是否已经运行（但不让检查失败阻止启动）
        try {
            if (checkStreamServerHealth()) {
                System.out.println("流服务器已经在运行中");
                userManuallyStopped = false; // 重置手动停止标志
                return true;
            }
        } catch (Exception e) {
            System.out.println("健康检查失败，继续尝试启动: " + e.getMessage());
        }

        try {
            // 获取项目根目录 - 使用多种方法尝试定位
            String projectRoot = getProjectRootDirectory();
            File scriptFile = new File(projectRoot, STREAM_SERVER_SCRIPT);

            System.out.println("尝试的项目根目录: " + projectRoot);
            System.out.println("查找脚本文件: " + scriptFile.getAbsolutePath());

            if (!scriptFile.exists()) {
                System.err.println("流服务器脚本不存在: " + scriptFile.getAbsolutePath());
                // 尝试其他可能的位置
                String[] possiblePaths = {
                    "C:\\Users\\<USER>\\Desktop\\EduFusionCenter",
                    System.getProperty("user.dir"),
                    System.getProperty("catalina.base"),
                    System.getProperty("catalina.home")
                };

                for (String path : possiblePaths) {
                    if (path != null) {
                        File testFile = new File(path, STREAM_SERVER_SCRIPT);
                        System.out.println("尝试路径: " + testFile.getAbsolutePath());
                        if (testFile.exists()) {
                            projectRoot = path;
                            scriptFile = testFile;
                            System.out.println("找到脚本文件: " + scriptFile.getAbsolutePath());
                            break;
                        }
                    }
                }

                if (!scriptFile.exists()) {
                    System.err.println("在所有可能的路径中都未找到流服务器脚本");
                    return false;
                }
            }

            System.out.println("启动流服务器脚本: " + scriptFile.getAbsolutePath());

            // 构建命令
            ProcessBuilder processBuilder = new ProcessBuilder(NODE_COMMAND, STREAM_SERVER_SCRIPT);
            processBuilder.directory(new File(projectRoot));
            processBuilder.redirectErrorStream(true);

            System.out.println("执行命令: " + NODE_COMMAND + " " + STREAM_SERVER_SCRIPT);
            System.out.println("工作目录: " + projectRoot);

            // 启动进程
            streamServerProcess = processBuilder.start();

            // 启动一个线程来读取进程输出
            Thread outputReader = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(streamServerProcess.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.out.println("[流服务器] " + line);
                    }
                } catch (IOException e) {
                    System.err.println("读取流服务器输出失败: " + e.getMessage());
                }
            });
            outputReader.setDaemon(true);
            outputReader.start();

            // 等待一段时间让服务器启动
            System.out.println("等待流服务器启动...");
            Thread.sleep(5000);

            // 检查进程是否还在运行
            if (streamServerProcess.isAlive()) {
                System.out.println("流服务器进程启动成功");

                // 验证服务器是否响应（更长的等待时间和更多重试）
                boolean isHealthy = false;
                System.out.println("开始健康检查...");
                for (int i = 0; i < 15; i++) {
                    try {
                        if (checkStreamServerHealth()) {
                            isHealthy = true;
                            System.out.println("健康检查在第 " + (i + 1) + " 次尝试时成功");
                            break;
                        }
                    } catch (Exception e) {
                        System.out.println("健康检查第 " + (i + 1) + " 次尝试失败: " + e.getMessage());
                    }
                    Thread.sleep(2000);
                }

                if (isHealthy) {
                    System.out.println("流服务器健康检查通过");
                    userManuallyStopped = false; // 重置手动停止标志
                    return true;
                } else {
                    System.err.println("流服务器启动但健康检查失败，但进程仍在运行，认为启动成功");
                    // 即使健康检查失败，如果进程在运行，也认为启动成功
                    userManuallyStopped = false; // 重置手动停止标志
                    return true;
                }
            } else {
                System.err.println("流服务器进程启动失败");
                return false;
            }

        } catch (Exception e) {
            System.err.println("启动流服务器异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean stopStreamServer() {
        System.out.println("尝试停止流服务器...");

        // 设置用户手动停止标志
        userManuallyStopped = true;

        if (streamServerProcess == null) {
            System.out.println("流服务器进程不存在");
            return true;
        }

        try {
            if (streamServerProcess.isAlive()) {
                System.out.println("正在停止流服务器进程...");
                streamServerProcess.destroy();

                // 等待进程结束
                boolean terminated = streamServerProcess.waitFor(10, TimeUnit.SECONDS);

                if (!terminated) {
                    System.out.println("强制终止流服务器进程...");
                    streamServerProcess.destroyForcibly();
                    streamServerProcess.waitFor(5, TimeUnit.SECONDS);
                }

                System.out.println("流服务器进程已停止");
            }

            streamServerProcess = null;
            return true;

        } catch (Exception e) {
            System.err.println("停止流服务器异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查用户是否手动停止了流服务器
     * @return 是否手动停止
     */
    public boolean isUserManuallyStopped() {
        return userManuallyStopped;
    }

    @Override
    public boolean isStreamServerRunning() {
        // 首先检查进程是否存在且活跃
        if (streamServerProcess != null && streamServerProcess.isAlive()) {
            // 进一步检查服务器是否响应
            return checkStreamServerHealth();
        }

        // 如果进程不存在，尝试通过健康检查确认
        return checkStreamServerHealth();
    }

    @Override
    public String getStreamServerHealthDetails() {
        try {
            String response = sendHttpRequest("/api/health", "GET", null);
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                StringBuilder details = new StringBuilder();
                details.append("服务器状态: 健康\n");
                details.append("消息: ").append(responseObj.get("message").getAsString()).append("\n");

                if (responseObj.has("ffmpeg")) {
                    details.append("FFmpeg状态: ").append(responseObj.get("ffmpeg").getAsString()).append("\n");
                }

                // 获取流状态信息
                try {
                    String statusResponse = sendHttpRequest("/api/stream/status", "GET", null);
                    JsonObject statusObj = gson.fromJson(statusResponse, JsonObject.class);

                    if (statusObj != null && statusObj.get("success").getAsBoolean()) {
                        JsonObject data = statusObj.getAsJsonObject("data");
                        details.append("活跃流数量: ").append(data.get("activeStreams").getAsInt()).append("\n");
                    }
                } catch (Exception e) {
                    details.append("获取流状态失败: ").append(e.getMessage()).append("\n");
                }

                return details.toString();
            } else {
                return "服务器状态: 不健康\n错误: " + (responseObj != null ? responseObj.get("error").getAsString() : "未知错误");
            }

        } catch (Exception e) {
            return "无法连接到流服务器: " + e.getMessage();
        }
    }

    @Override
    public boolean clearAllVideoCache() {
        System.out.println("开始清除所有视频缓存...");

        try {
            // 首先检查流服务器是否可用
            if (!checkStreamServerHealth()) {
                System.out.println("流服务器不可用，无法清除缓存");
                return false;
            }

            // 发送清除缓存请求到Node.js服务器
            String response = sendHttpRequest("/api/cache/clear", "POST", null);
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                System.out.println("视频缓存清除成功");
                return true;
            } else {
                String error = responseObj != null ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("清除视频缓存失败: " + error);
                return false;
            }

        } catch (Exception e) {
            System.err.println("清除视频缓存异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean stopAllActiveStreams() {
        System.out.println("开始停止所有活跃的视频流...");

        try {
            // 首先检查流服务器是否可用
            if (!checkStreamServerHealth()) {
                System.out.println("流服务器不可用，无法停止流");
                return false;
            }

            // 发送停止所有流的请求到Node.js服务器
            String response = sendHttpRequest("/api/stream/stop-all", "POST", null);
            JsonObject responseObj = gson.fromJson(response, JsonObject.class);

            if (responseObj != null && responseObj.get("success").getAsBoolean()) {
                // 更新数据库中所有摄像头的流状态
                List<Camera> cameras = cameraDao.getAllCameras();
                if (cameras != null) {
                    for (Camera camera : cameras) {
                        if (camera.getStreamStatus() == 1) { // 只更新正在运行的流
                            cameraDao.updateCameraStreamStatus(camera.getId(), 0, null);
                        }
                    }
                }

                System.out.println("所有活跃视频流停止成功");
                return true;
            } else {
                String error = responseObj != null ? responseObj.get("error").getAsString() : "未知错误";
                System.err.println("停止所有活跃视频流失败: " + error);
                return false;
            }

        } catch (Exception e) {
            System.err.println("停止所有活跃视频流异常: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
